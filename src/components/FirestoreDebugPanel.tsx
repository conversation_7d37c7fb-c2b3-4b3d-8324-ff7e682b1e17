"use client";

import React, { useState, useEffect } from 'react';
import { firestoreMonitor } from '@/lib/firestore-monitor';
import { resetFirestore, diagnoseFirestore } from '@/lib/firestore-reset';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, Activity, Database, Trash2, RotateCcw, Zap } from 'lucide-react';

export function FirestoreDebugPanel() {
  const [summary, setSummary] = useState(firestoreMonitor.getSummary());
  const [isVisible, setIsVisible] = useState(false);
  const [isResetting, setIsResetting] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setSummary(firestoreMonitor.getSummary());
    }, 1000); // Update every second

    return () => clearInterval(interval);
  }, []);

  const handleForceReset = async () => {
    setIsResetting(true);
    try {
      await resetFirestore();
      // Refresh the page after reset to ensure clean state
      window.location.reload();
    } catch (error) {
      console.error('Failed to reset Firestore:', error);
    } finally {
      setIsResetting(false);
    }
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          variant="outline"
          size="sm"
          className="bg-background/80 backdrop-blur-sm"
        >
          <Database className="h-4 w-4 mr-2" />
          Firestore Debug
        </Button>
      </div>
    );
  }

  const hasIssues = summary.potentialIssues.length > 0;

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96 max-h-96 overflow-auto">
      <Card className="bg-background/95 backdrop-blur-sm border-2">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center gap-2">
              <Database className="h-4 w-4" />
              Firestore Monitor
              {hasIssues && <AlertTriangle className="h-4 w-4 text-yellow-500" />}
            </CardTitle>
            <div className="flex gap-1">
              <Button
                onClick={handleForceReset}
                variant="destructive"
                size="sm"
                disabled={isResetting}
                title="Force reset Firestore (will reload page)"
              >
                {isResetting ? 'Resetting...' : 'Reset'}
              </Button>
              <Button
                onClick={() => firestoreMonitor.clear()}
                variant="ghost"
                size="sm"
                title="Clear event log"
              >
                Clear
              </Button>
              <Button
                onClick={() => setIsVisible(false)}
                variant="ghost"
                size="sm"
              >
                ×
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-3 text-xs">
          {/* Summary Stats */}
          <div className="grid grid-cols-2 gap-2">
            <div className="text-center p-2 bg-muted rounded">
              <div className="font-semibold">{summary.totalEvents}</div>
              <div className="text-muted-foreground">Total Events</div>
            </div>
            <div className="text-center p-2 bg-muted rounded">
              <div className="font-semibold">{summary.eventsByType.read || 0}</div>
              <div className="text-muted-foreground">Reads</div>
            </div>
          </div>

          {/* Event Types */}
          <div>
            <div className="font-semibold mb-1">Event Types:</div>
            <div className="flex flex-wrap gap-1">
              {Object.entries(summary.eventsByType).map(([type, count]) => (
                <Badge key={type} variant="secondary" className="text-xs">
                  {type}: {count}
                </Badge>
              ))}
            </div>
          </div>

          {/* Potential Issues */}
          {hasIssues && (
            <div>
              <div className="font-semibold mb-1 text-yellow-600 flex items-center gap-1">
                <AlertTriangle className="h-3 w-3" />
                Issues:
              </div>
              <div className="space-y-1">
                {summary.potentialIssues.map((issue, index) => (
                  <div key={index} className="text-yellow-600 text-xs bg-yellow-50 p-1 rounded">
                    {issue}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Recent Activity */}
          <div>
            <div className="font-semibold mb-1 flex items-center gap-1">
              <Activity className="h-3 w-3" />
              Recent Activity:
            </div>
            <div className="space-y-1 max-h-32 overflow-auto">
              {summary.recentActivity.map((event, index) => (
                <div key={index} className="text-xs bg-muted p-1 rounded">
                  <div className="flex justify-between">
                    <span className="font-mono">{event.type}</span>
                    <span className="text-muted-foreground">
                      {new Date(event.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                  {event.details && (
                    <div className="text-muted-foreground mt-1">
                      {JSON.stringify(event.details, null, 0).slice(0, 100)}
                      {JSON.stringify(event.details).length > 100 && '...'}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
